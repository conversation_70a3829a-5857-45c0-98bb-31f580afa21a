#!/usr/bin/env python3
"""
脚本用于从CSV文件中下载封面、智能封面和视频数据
"""

import os
import sys
from pathlib import Path
import pandas as pd
from afts import Afts
from PIL import Image
from io import BytesIO
import time
from tqdm import tqdm

# AFTS配置
endpoint_config = {
    "upload_endpoint_source": "mass.alipay.com",
    "download_endpoint_source": "mdn.alipayobjects.com",
    "authority_endpoint": "mmtcapi.alipay.com"
}
biz_key = "content_liveface"
biz_secret = "31cedb0f8bc24b778af865d8c5704705"

# 初始化AFTS客户端
_afts = Afts(
    biz_key=biz_key,
    biz_secret=biz_secret,
    endpoint_config=endpoint_config
)

def download_from_afts(afts_id, save_path):
    """
    从AFTS下载图片并保存到指定路径

    Args:
        afts_id (str): AFTS文件ID
        save_path (str): 保存路径

    Returns:
        bool: 下载是否成功
    """
    try:
        content = _afts.download_file(afts_id)

        # 保存图片
        with open(save_path, 'wb') as f:
            f.write(content)

        return True
    except Exception as e:
        return False

def create_output_dir(base_dir):
    """创建输出目录"""
    output_dir = Path(base_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    return output_dir

def download_cover_pairs(csv_file, output_dir="downloaded_images", max_downloads=None, download_video=False):
    """
    从CSV文件下载封面、智能封面和视频文件

    Args:
        csv_file (str): CSV文件路径
        output_dir (str): 输出目录
        max_downloads (int): 最大下载数量，None表示下载全部
        download_video (bool): 是否同时下载视频文件
    """
    # 创建输出目录
    output_path = create_output_dir(output_dir)

    # 统计信息
    total_rows = 0
    successful_covers = 0
    successful_smart_covers = 0
    successful_videos = 0
    failed_downloads = []

    print(f"开始从 {csv_file} 下载图片...")
    if download_video:
        print("同时下载视频文件")
    print(f"文件保存目录: {output_path}")

    try:
        # 使用pandas读取CSV文件
        df = pd.read_csv(csv_file)

        # 如果指定了最大下载数量，则截取数据
        if max_downloads:
            df = df.head(max_downloads)

        total_rows = len(df)

        # 使用tqdm显示进度条
        with tqdm(total=total_rows, desc="下载进度", unit="行") as pbar:
            for i, row in df.iterrows():
                ctt_id = row['ctt_id']
                cover_id = row['cover_id']
                smart_cover_id = row['smart_cover_id']

                # 获取视频ID（如果存在）
                video_id = row.get('video_id', None) if download_video else None

                pbar.set_description(f"处理 {ctt_id[:20]}...")

                # 下载封面
                if pd.notna(cover_id) and cover_id:
                    cover_filename = f"{ctt_id}_cover.jpg"
                    cover_path = output_path / cover_filename

                    if download_from_afts(cover_id, cover_path):
                        successful_covers += 1
                    else:
                        failed_downloads.append(f"封面: {ctt_id} - {cover_id}")

                # 下载智能封面
                if pd.notna(smart_cover_id) and smart_cover_id:
                    smart_cover_filename = f"{ctt_id}_smart_cover.jpg"
                    smart_cover_path = output_path / smart_cover_filename

                    if download_from_afts(smart_cover_id, smart_cover_path):
                        successful_smart_covers += 1
                    else:
                        failed_downloads.append(f"智能封面: {ctt_id} - {smart_cover_id}")

                # 下载视频（如果启用了视频下载选项）
                if download_video and pd.notna(video_id) and video_id:
                    video_filename = f"{ctt_id}_video.mp4"
                    video_path = output_path / video_filename

                    if download_from_afts(video_id, video_path):
                        successful_videos += 1
                    else:
                        failed_downloads.append(f"视频: {ctt_id} - {video_id}")

                # 更新进度条
                pbar.update(1)
                # pbar.set_postfix({
                #     '封面': successful_covers,
                #     '智能封面': successful_smart_covers,
                #     '失败': len(failed_downloads)
                # })

                # 添加延迟避免请求过于频繁
                time.sleep(0.1)

    except FileNotFoundError:
        print(f"错误: 找不到CSV文件 {csv_file}")
        return
    except Exception as e:
        print(f"处理CSV文件时出错: {e}")
        return
    
    # 输出统计信息
    print(f"\n=== 下载完成 ===")
    print(f"总处理行数: {total_rows}")
    print(f"成功下载封面: {successful_covers}")
    print(f"成功下载智能封面: {successful_smart_covers}")
    if download_video:
        print(f"成功下载视频: {successful_videos}")
    print(f"失败下载数: {len(failed_downloads)}")
    
    if failed_downloads:
        print(f"\n失败的下载:")
        for failed in failed_downloads:
            print(f"  - {failed}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='下载封面、智能封面和视频文件')
    parser.add_argument('csv_file', help='CSV文件路径')
    parser.add_argument('-o', '--output', default='downloads/cover_pairs',
                       help='输出目录 (默认: downloads/cover_pairs)')
    parser.add_argument('-n', '--max-downloads', type=int,
                       help='最大下载数量限制')
    parser.add_argument('-v', '--download-video', action='store_true',
                       help='同时下载视频文件')
    
    args = parser.parse_args()
    
    # 检查CSV文件是否存在
    if not os.path.exists(args.csv_file):
        print(f"错误: CSV文件不存在: {args.csv_file}")
        sys.exit(1)
    
    # 开始下载
    download_cover_pairs(args.csv_file, args.output, args.max_downloads, args.download_video)

if __name__ == "__main__":
    main()
